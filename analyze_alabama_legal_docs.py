#!/usr/bin/env python3
"""
Script to analyze txt files for Alabama legal context and generate a CSV report.
"""

import os
import csv
import re
from pathlib import Path

def is_alabama_legal_document(content, filename):
    """
    Analyze document content to determine if it's Alabama legal/illegal context.
    Returns tuple: (is_alabama_legal, classification, comment)
    """
    content_lower = content.lower()
    
    # Alabama legal indicators
    alabama_indicators = [
        'supreme court of alabama',
        'alabama appellate courts',
        'alabama circuit court',
        'alabama district court',
        'alabama probate court',
        'alabama court of',
        'ala. code',
        'alabama code',
        'montgomery, alabama',
        'alabama supreme court',
        'alabama civil appeals',
        'alabama criminal appeals',
        'alabama law',
        'alabama statute',
        'alabama rules',
        'alabama constitution'
    ]
    
    # Federal court indicators that might involve Alabama
    federal_alabama_indicators = [
        'middle district of alabama',
        'northern district of alabama',
        'southern district of alabama',
        'eleventh circuit.*alabama',
        'alabama.*federal'
    ]
    
    # General legal document indicators
    legal_indicators = [
        'plaintiff',
        'defendant',
        'appellant',
        'appellee',
        'circuit court',
        'district court',
        'supreme court',
        'court of appeals',
        'judgment',
        'appeal from',
        'case:',
        'docket',
        'opinion',
        'justice',
        'judge',
        'versus',
        'v.',
        'civil action',
        'criminal case'
    ]
    
    # Check for Alabama-specific legal content
    alabama_matches = []
    for indicator in alabama_indicators:
        if re.search(indicator, content_lower):
            alabama_matches.append(indicator)
    
    # Check for federal courts involving Alabama
    federal_alabama_matches = []
    for indicator in federal_alabama_indicators:
        if re.search(indicator, content_lower):
            federal_alabama_matches.append(indicator)
    
    # Check for general legal content
    legal_matches = []
    for indicator in legal_indicators:
        if re.search(indicator, content_lower):
            legal_matches.append(indicator)
    
    # Determine classification
    if alabama_matches:
        return True, "Alabama State Legal", f"Alabama legal document. Found indicators: {', '.join(alabama_matches[:3])}"
    elif federal_alabama_matches:
        return True, "Federal Legal (Alabama-related)", f"Federal court document involving Alabama. Found: {', '.join(federal_alabama_matches[:3])}"
    elif legal_matches:
        # Check if it mentions Alabama in any context
        if 'alabama' in content_lower:
            return True, "Legal Document (Alabama-mentioned)", f"Legal document mentioning Alabama. Legal indicators: {', '.join(legal_matches[:3])}"
        else:
            return False, "Legal Document (Non-Alabama)", f"Legal document but not Alabama-related. Found: {', '.join(legal_matches[:3])}"
    else:
        # Check if Alabama is mentioned at all
        if 'alabama' in content_lower:
            return True, "Non-Legal (Alabama-mentioned)", "Document mentions Alabama but doesn't appear to be legal"
        else:
            return False, "Non-Legal", "Document doesn't appear to be legal or Alabama-related"

def analyze_files():
    """
    Analyze all txt files in the current directory.
    """
    current_dir = Path('.')
    txt_files = list(current_dir.glob('*.txt'))
    
    print(f"Found {len(txt_files)} txt files to analyze...")
    
    results = []
    
    for i, txt_file in enumerate(txt_files, 1):
        print(f"Processing {i}/{len(txt_files)}: {txt_file.name}")
        
        try:
            with open(txt_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            is_alabama, classification, comment = is_alabama_legal_document(content, txt_file.name)
            
            results.append({
                'file_path': str(txt_file),
                'filename': txt_file.name,
                'is_alabama_legal': 'Yes' if is_alabama else 'No',
                'classification': classification,
                'comment': comment,
                'file_size_bytes': txt_file.stat().st_size,
                'content_preview': content[:200].replace('\n', ' ').replace('\r', ' ').strip()
            })
            
        except Exception as e:
            results.append({
                'file_path': str(txt_file),
                'filename': txt_file.name,
                'is_alabama_legal': 'Error',
                'classification': 'Error',
                'comment': f"Error reading file: {str(e)}",
                'file_size_bytes': 0,
                'content_preview': ''
            })
    
    return results

def generate_csv_report(results, output_file='alabama_legal_analysis.csv'):
    """
    Generate CSV report from analysis results.
    """
    fieldnames = [
        'file_path',
        'filename', 
        'is_alabama_legal',
        'classification',
        'comment',
        'file_size_bytes',
        'content_preview'
    ]
    
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(results)
    
    print(f"\nCSV report generated: {output_file}")
    
    # Print summary statistics
    alabama_legal_count = sum(1 for r in results if r['is_alabama_legal'] == 'Yes')
    total_count = len(results)
    error_count = sum(1 for r in results if r['is_alabama_legal'] == 'Error')
    
    print(f"\nSummary:")
    print(f"Total files analyzed: {total_count}")
    print(f"Alabama legal/related documents: {alabama_legal_count}")
    print(f"Non-Alabama legal documents: {total_count - alabama_legal_count - error_count}")
    print(f"Errors: {error_count}")
    print(f"Percentage Alabama-related: {(alabama_legal_count/total_count)*100:.1f}%")

if __name__ == "__main__":
    print("Starting Alabama Legal Document Analysis...")
    results = analyze_files()
    generate_csv_report(results)
    print("Analysis complete!")
