#!/usr/bin/env python3
"""
Script to move files to appropriate folders based on Alabama classification
"""

import os
import shutil
import csv

def move_files_based_on_classification():
    """
    Read the improved analysis CSV and move files to appropriate folders
    """
    
    # Create folders if they don't exist
    alabama_folder = "Alabama_Related_Files"
    non_alabama_folder = "Non_Alabama_Files"
    
    os.makedirs(alabama_folder, exist_ok=True)
    os.makedirs(non_alabama_folder, exist_ok=True)
    
    # Read the classification results
    csv_file = "improved_file_analysis.csv"
    
    if not os.path.exists(csv_file):
        print(f"Error: {csv_file} not found!")
        return
    
    moved_alabama = 0
    moved_non_alabama = 0
    errors = 0
    
    print("Moving files based on classification...")
    
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        for row in reader:
            filename = row['filename']
            is_alabama_related = row['is_alabama_related']
            classification = row['classification']
            
            # Skip if file doesn't exist
            if not os.path.exists(filename):
                print(f"Warning: File {filename} not found, skipping...")
                errors += 1
                continue
            
            try:
                if is_alabama_related == 'YES':
                    # Move to Alabama folder
                    destination = os.path.join(alabama_folder, filename)
                    shutil.move(filename, destination)
                    moved_alabama += 1
                    if moved_alabama % 50 == 0:
                        print(f"Moved {moved_alabama} Alabama-related files...")
                
                elif is_alabama_related == 'NO':
                    # Move to Non-Alabama folder
                    destination = os.path.join(non_alabama_folder, filename)
                    shutil.move(filename, destination)
                    moved_non_alabama += 1
                    if moved_non_alabama % 10 == 0:
                        print(f"Moved {moved_non_alabama} non-Alabama files...")
                
                else:
                    print(f"Unknown classification for {filename}: {is_alabama_related}")
                    errors += 1
                    
            except Exception as e:
                print(f"Error moving {filename}: {str(e)}")
                errors += 1
    
    print(f"\nFile movement complete!")
    print(f"Alabama-related files moved: {moved_alabama}")
    print(f"Non-Alabama files moved: {moved_non_alabama}")
    print(f"Errors: {errors}")
    
    # Create summary files in each folder
    create_folder_summaries(alabama_folder, non_alabama_folder)

def create_folder_summaries(alabama_folder, non_alabama_folder):
    """
    Create summary files in each folder
    """
    
    # Alabama folder summary
    alabama_files = [f for f in os.listdir(alabama_folder) if f.endswith('.txt')]
    with open(os.path.join(alabama_folder, "README.txt"), 'w', encoding='utf-8') as f:
        f.write("ALABAMA-RELATED LEGAL DOCUMENTS\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"This folder contains {len(alabama_files)} files that are related to Alabama jurisdiction.\n\n")
        f.write("These include:\n")
        f.write("- Alabama State Court documents (Supreme Court, Civil Appeals, Criminal Appeals)\n")
        f.write("- Federal Court documents from Alabama districts\n")
        f.write("- Documents with multiple Alabama Code references\n\n")
        f.write("Files in this folder:\n")
        f.write("-" * 30 + "\n")
        for filename in sorted(alabama_files):
            f.write(f"  {filename}\n")
    
    # Non-Alabama folder summary
    non_alabama_files = [f for f in os.listdir(non_alabama_folder) if f.endswith('.txt')]
    with open(os.path.join(non_alabama_folder, "README.txt"), 'w', encoding='utf-8') as f:
        f.write("NON-ALABAMA LEGAL DOCUMENTS\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"This folder contains {len(non_alabama_files)} files that are NOT primarily related to Alabama jurisdiction.\n\n")
        f.write("These include:\n")
        f.write("- Federal court cases from other circuits\n")
        f.write("- State court cases from other states\n")
        f.write("- Cases that mention Alabama only in legal citations\n\n")
        f.write("Files in this folder:\n")
        f.write("-" * 30 + "\n")
        for filename in sorted(non_alabama_files):
            f.write(f"  {filename}\n")
    
    print(f"Created README.txt in both folders")

def verify_move():
    """
    Verify the file movement was successful
    """
    alabama_folder = "Alabama_Related_Files"
    non_alabama_folder = "Non_Alabama_Files"
    
    alabama_count = len([f for f in os.listdir(alabama_folder) if f.endswith('.txt')])
    non_alabama_count = len([f for f in os.listdir(non_alabama_folder) if f.endswith('.txt')])
    remaining_count = len([f for f in os.listdir('.') if f.endswith('.txt')])
    
    print(f"\nVerification:")
    print(f"Files in Alabama_Related_Files folder: {alabama_count}")
    print(f"Files in Non_Alabama_Files folder: {non_alabama_count}")
    print(f"Files remaining in root directory: {remaining_count}")
    print(f"Total files processed: {alabama_count + non_alabama_count}")

if __name__ == "__main__":
    move_files_based_on_classification()
    verify_move()
