#!/usr/bin/env python3
"""
Script to analyze all txt files and categorize them as Alabama-related or not.
"""

import os
import csv

def analyze_file_for_alabama(file_path):
    """
    Analyze a single file to determine if it's Alabama-related
    Returns: (is_alabama_related, classification, details)
    """
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Convert to lowercase for case-insensitive matching
        content_lower = content.lower()
        
        # Strong Alabama indicators (primary jurisdiction)
        strong_alabama_indicators = [
            'supreme court of alabama',
            'alabama supreme court',
            'court of civil appeals of alabama',
            'alabama court of civil appeals',
            'court of criminal appeals of alabama',
            'alabama court of criminal appeals',
            'alabama circuit court',
            'alabama probate court',
            'alabama appellate courts',
            'montgomery, alabama 36104',
            'dexter avenue, montgomery, alabama',
            'ala. code',
            'alabama code'
        ]
        
        # Federal Alabama indicators
        federal_alabama_indicators = [
            'northern district of alabama',
            'middle district of alabama', 
            'southern district of alabama',
            'n.d. ala.',
            'm.d. ala.',
            's.d. ala.',
            'district court for the northern district of alabama',
            'district court for the middle district of alabama',
            'district court for the southern district of alabama'
        ]
        
        # Check for strong Alabama indicators first
        for indicator in strong_alabama_indicators:
            if indicator in content_lower:
                return True, "Alabama State Legal", f"Contains: {indicator}"
        
        # Check for federal Alabama indicators
        for indicator in federal_alabama_indicators:
            if indicator in content_lower:
                return True, "Federal Legal (Alabama-related)", f"Contains: {indicator}"
        
        # Check for general Alabama mentions (but be more restrictive)
        if 'alabama' in content_lower:
            # Check if this is actually an Alabama case or just mentions Alabama
            if any(phrase in content_lower for phrase in [
                'appeal from', 'circuit court', 'district court', 'supreme court',
                'court of appeals', 'probate court', 'juvenile court'
            ]):
                # Look for Alabama in context of courts
                lines = content_lower.split('\n')
                for line in lines:
                    if 'alabama' in line and any(court_word in line for court_word in [
                        'court', 'appeal', 'circuit', 'district', 'supreme', 'probate'
                    ]):
                        return True, "Legal Document (Alabama-mentioned)", f"Alabama mentioned in legal context"
        
        return False, "Non-Alabama Legal", "No Alabama indicators found"
        
    except Exception as e:
        return False, "Error", f"Error reading file: {str(e)}"

def main():
    # Get all txt files
    txt_files = [f for f in os.listdir('.') if f.endswith('.txt')]
    
    alabama_related = []
    non_alabama_related = []
    
    print(f"Analyzing {len(txt_files)} files...")
    
    for i, filename in enumerate(txt_files, 1):
        if i % 50 == 0:
            print(f"Processed {i}/{len(txt_files)} files...")
        
        is_alabama, classification, details = analyze_file_for_alabama(filename)
        
        file_info = {
            'filename': filename,
            'classification': classification,
            'details': details
        }
        
        if is_alabama:
            alabama_related.append(file_info)
        else:
            non_alabama_related.append(file_info)
    
    # Create detailed CSV analysis
    with open('detailed_file_analysis.csv', 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['filename', 'is_alabama_related', 'classification', 'details']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for file_info in alabama_related:
            writer.writerow({
                'filename': file_info['filename'],
                'is_alabama_related': 'YES',
                'classification': file_info['classification'],
                'details': file_info['details']
            })
        
        for file_info in non_alabama_related:
            writer.writerow({
                'filename': file_info['filename'],
                'is_alabama_related': 'NO',
                'classification': file_info['classification'],
                'details': file_info['details']
            })
    
    # Create Alabama-related files list
    with open('alabama_related_files.txt', 'w', encoding='utf-8') as f:
        f.write("ALABAMA-RELATED LEGAL DOCUMENTS\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"Total Alabama-related files: {len(alabama_related)}\n\n")
        
        # Group by classification
        classifications = {}
        for file_info in alabama_related:
            classification = file_info['classification']
            if classification not in classifications:
                classifications[classification] = []
            classifications[classification].append(file_info)
        
        for classification, files in classifications.items():
            f.write(f"\n{classification} ({len(files)} files):\n")
            f.write("-" * 40 + "\n")
            for file_info in sorted(files, key=lambda x: x['filename']):
                f.write(f"  {file_info['filename']} - {file_info['details']}\n")
    
    # Create non-Alabama files list
    with open('non_alabama_files.txt', 'w', encoding='utf-8') as f:
        f.write("NON-ALABAMA LEGAL DOCUMENTS\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"Total non-Alabama files: {len(non_alabama_related)}\n\n")
        
        # Group by classification
        classifications = {}
        for file_info in non_alabama_related:
            classification = file_info['classification']
            if classification not in classifications:
                classifications[classification] = []
            classifications[classification].append(file_info)
        
        for classification, files in classifications.items():
            f.write(f"\n{classification} ({len(files)} files):\n")
            f.write("-" * 40 + "\n")
            for file_info in sorted(files, key=lambda x: x['filename']):
                f.write(f"  {file_info['filename']} - {file_info['details']}\n")
    
    print(f"\nAnalysis complete!")
    print(f"Alabama-related files: {len(alabama_related)}")
    print(f"Non-Alabama files: {len(non_alabama_related)}")
    print(f"Total files analyzed: {len(txt_files)}")
    
    print(f"\nFiles created:")
    print(f"  - alabama_related_files.txt")
    print(f"  - non_alabama_files.txt") 
    print(f"  - detailed_file_analysis.csv")

if __name__ == "__main__":
    main()
