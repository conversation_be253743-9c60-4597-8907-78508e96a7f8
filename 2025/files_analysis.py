
import os
import csv
import re

def analyze_file_for_alabama(file_path):
    """
    Analyze a single file to determine if it's Alabama-related
    Returns: (is_alabama_related, classification, details)
    """
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Get first 20 lines to check primary jurisdiction
        lines = content.split('\n')
        header_content = '\n'.join(lines[:20]).lower()
        full_content_lower = content.lower()
        
        # Primary Alabama court indicators (must appear in header/early content)
        primary_alabama_indicators = [
            'supreme court of alabama',
            'alabama supreme court',
            'court of civil appeals of alabama',
            'alabama court of civil appeals',
            'court of criminal appeals of alabama', 
            'alabama court of criminal appeals',
            'alabama circuit court',
            'alabama probate court',
            'alabama appellate courts',
            'montgomery, alabama 36104',
            'dexter avenue, montgomery, alabama'
        ]
        
        # Federal Alabama indicators (must appear in header/early content)
        federal_alabama_indicators = [
            'northern district of alabama',
            'middle district of alabama', 
            'southern district of alabama',
            'n.d. ala.',
            'm.d. ala.',
            's.d. ala.',
            'district court for the northern district of alabama',
            'district court for the middle district of alabama',
            'district court for the southern district of alabama'
        ]
        
        # Check for primary Alabama jurisdiction in header
        for indicator in primary_alabama_indicators:
            if indicator in header_content:
                return True, "Alabama State Legal", f"Primary jurisdiction: {indicator}"
        
        # Check for federal Alabama jurisdiction in header
        for indicator in federal_alabama_indicators:
            if indicator in header_content:
                return True, "Federal Legal (Alabama-related)", f"Primary jurisdiction: {indicator}"
        
        # Check for Alabama legal codes (these indicate Alabama law application)
        if 'ala. code' in full_content_lower or 'alabama code' in full_content_lower:
            # But make sure it's not just a citation - check if it appears multiple times
            ala_code_count = full_content_lower.count('ala. code') + full_content_lower.count('alabama code')
            if ala_code_count >= 3:  # Multiple references suggest Alabama law applies
                return True, "Alabama State Legal", f"Multiple Alabama Code references ({ala_code_count})"
        
        # Check for non-Alabama primary jurisdiction
        non_alabama_courts = [
            'court of appeals of tennessee',
            'supreme court of tennessee',
            'court of appeals of florida',
            'supreme court of florida',
            'court of appeals of georgia',
            'supreme court of georgia',
            'court of appeals of texas',
            'supreme court of texas',
            'court of appeals of california',
            'supreme court of california',
            'united states court of appeals',
            'u.s. court of appeals',
            'district court for the',
            'bankruptcy court for the'
        ]
        
        # If we find non-Alabama primary jurisdiction, it's likely not Alabama-related
        for court in non_alabama_courts:
            if court in header_content:
                # Double-check: if Alabama is mentioned but it's clearly another state's court
                if 'alabama' in full_content_lower:
                    return False, "Non-Alabama Legal (mentions Alabama)", f"Primary jurisdiction: {court}, but mentions Alabama"
                else:
                    return False, "Non-Alabama Legal", f"Primary jurisdiction: {court}"
        
        # If Alabama is mentioned but no clear primary jurisdiction found
        if 'alabama' in full_content_lower:
            return True, "Legal Document (Alabama-mentioned)", "Alabama mentioned but unclear jurisdiction"
        
        return False, "Non-Alabama Legal", "No Alabama indicators found"
        
    except Exception as e:
        return False, "Error", f"Error reading file: {str(e)}"

def main():
    # Get all txt files
    txt_files = [f for f in os.listdir('.') if f.endswith('.txt')]
    
    alabama_related = []
    non_alabama_related = []
    
    print(f"Analyzing {len(txt_files)} files with improved logic...")
    
    for i, filename in enumerate(txt_files, 1):
        if i % 50 == 0:
            print(f"Processed {i}/{len(txt_files)} files...")
        
        is_alabama, classification, details = analyze_file_for_alabama(filename)
        
        file_info = {
            'filename': filename,
            'classification': classification,
            'details': details
        }
        
        if is_alabama:
            alabama_related.append(file_info)
        else:
            non_alabama_related.append(file_info)
    
    # Create detailed CSV analysis
    with open('improved_file_analysis.csv', 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['filename', 'is_alabama_related', 'classification', 'details']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for file_info in alabama_related:
            writer.writerow({
                'filename': file_info['filename'],
                'is_alabama_related': 'YES',
                'classification': file_info['classification'],
                'details': file_info['details']
            })
        
        for file_info in non_alabama_related:
            writer.writerow({
                'filename': file_info['filename'],
                'is_alabama_related': 'NO',
                'classification': file_info['classification'],
                'details': file_info['details']
            })
    
    # Create Alabama-related files list
    with open('alabama_related_files_improved.txt', 'w', encoding='utf-8') as f:
        f.write("ALABAMA-RELATED LEGAL DOCUMENTS (IMPROVED ANALYSIS)\n")
        f.write("=" * 60 + "\n\n")
        f.write(f"Total Alabama-related files: {len(alabama_related)}\n\n")
        
        # Group by classification
        classifications = {}
        for file_info in alabama_related:
            classification = file_info['classification']
            if classification not in classifications:
                classifications[classification] = []
            classifications[classification].append(file_info)
        
        for classification, files in classifications.items():
            f.write(f"\n{classification} ({len(files)} files):\n")
            f.write("-" * 50 + "\n")
            for file_info in sorted(files, key=lambda x: x['filename']):
                f.write(f"  {file_info['filename']} - {file_info['details']}\n")
    
    # Create non-Alabama files list
    with open('non_alabama_files_improved.txt', 'w', encoding='utf-8') as f:
        f.write("NON-ALABAMA LEGAL DOCUMENTS (IMPROVED ANALYSIS)\n")
        f.write("=" * 60 + "\n\n")
        f.write(f"Total non-Alabama files: {len(non_alabama_related)}\n\n")
        
        # Group by classification
        classifications = {}
        for file_info in non_alabama_related:
            classification = file_info['classification']
            if classification not in classifications:
                classifications[classification] = []
            classifications[classification].append(file_info)
        
        for classification, files in classifications.items():
            f.write(f"\n{classification} ({len(files)} files):\n")
            f.write("-" * 50 + "\n")
            for file_info in sorted(files, key=lambda x: x['filename']):
                f.write(f"  {file_info['filename']} - {file_info['details']}\n")
    
    print(f"\nImproved analysis complete!")
    print(f"Alabama-related files: {len(alabama_related)}")
    print(f"Non-Alabama files: {len(non_alabama_related)}")
    print(f"Total files analyzed: {len(txt_files)}")
    
    print(f"\nFiles created:")
    print(f"  - alabama_related_files_improved.txt")
    print(f"  - non_alabama_files_improved.txt") 
    print(f"  - improved_file_analysis.csv")

if __name__ == "__main__":
    main()
